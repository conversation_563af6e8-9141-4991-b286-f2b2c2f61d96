#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF JSON 转 Markdown 转换器
将从 PDF 解析的 JSON 格式转换为 Markdown 文件
"""

import json
import re
from typing import List, Dict, Any


def extract_text_from_textlines(textlines: List[Dict[str, Any]]) -> List[str]:
    """从 textlines 中提取文本内容，返回文本行列表"""
    if not textlines:
        return []

    texts = []
    for line in textlines:
        text = line.get('text', '').strip()
        if text:
            # 检查是否为粗体
            is_bold = line.get('bold', False)
            if is_bold and text:
                text = f"**{text}**"
            texts.append(text)

    return texts


def process_tree_node(node: Dict[str, Any], level: int = 0) -> List[str]:
    """递归处理树节点，提取文本内容"""
    content = []
    
    if not node:
        return content
    
    # 处理当前节点的数据
    if 'data' in node and node['data']:
        data = node['data']
        
        # 处理 textlines
        if 'textlines' in data and data['textlines']:
            text = extract_text_from_textlines(data['textlines'])
            if text.strip():
                # 根据节点类型和层级决定格式
                node_type = node.get('type', '')
                
                if node_type == 'title' and level == 1:
                    # 主标题
                    content.append(f"# {text}")
                elif node_type == 'title' and level > 1:
                    # 子标题
                    content.append(f"{'#' * min(level + 1, 6)} {text}")
                elif text.startswith('●'):
                    # 列表项
                    content.append(f"- {text[1:].strip()}")
                elif text.startswith('【') and text.endswith('】'):
                    # 特殊标记的内容，作为子标题
                    content.append(f"## {text}")
                else:
                    # 普通段落
                    content.append(text)
    
    # 递归处理子节点
    if 'children' in node and node['children']:
        for child in node['children']:
            child_content = process_tree_node(child, level + 1)
            content.extend(child_content)
    
    return content


def clean_and_merge_paragraphs(content_lines: List[str]) -> List[str]:
    """清理和合并段落"""
    if not content_lines:
        return []
    
    merged = []
    current_paragraph = []
    
    for line in content_lines:
        line = line.strip()
        if not line:
            continue
            
        # 检查是否是标题或列表项
        is_header = line.startswith('#')
        is_list_item = line.startswith('-')
        is_special_block = line.startswith('**') and line.endswith('**')
        
        # 如果是标题、列表项或特殊块，先处理当前段落
        if is_header or is_list_item or is_special_block:
            if current_paragraph:
                merged.append(''.join(current_paragraph))
                current_paragraph = []
            merged.append(line)
        else:
            # 普通文本，添加到当前段落
            current_paragraph.append(line)
    
    # 处理最后的段落
    if current_paragraph:
        merged.append(''.join(current_paragraph))
    
    return merged


def json_to_markdown(json_file_path: str, markdown_file_path: str):
    """将 JSON 文件转换为 Markdown 文件"""
    try:
        # 读取 JSON 文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取树结构
        tree = data.get('tree', {})
        root = tree.get('root', {})
        
        # 处理树结构，提取内容
        content_lines = []
        if 'children' in root:
            for child in root['children']:
                child_content = process_tree_node(child, 0)
                content_lines.extend(child_content)
        
        # 清理和合并段落
        merged_content = clean_and_merge_paragraphs(content_lines)
        
        # 生成最终的 Markdown 内容
        markdown_content = []
        for i, line in enumerate(merged_content):
            markdown_content.append(line)
            
            # 在不同类型的内容之间添加空行
            if i < len(merged_content) - 1:
                next_line = merged_content[i + 1]
                current_is_header = line.startswith('#')
                next_is_header = next_line.startswith('#')
                current_is_list = line.startswith('-')
                next_is_list = next_line.startswith('-')
                
                # 在标题后、标题前、列表和段落之间添加空行
                if (current_is_header or next_is_header or 
                    (current_is_list != next_is_list)):
                    markdown_content.append('')
        
        # 写入 Markdown 文件
        with open(markdown_file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))
        
        print(f"转换完成！Markdown 文件已保存到: {markdown_file_path}")
        print(f"共处理了 {len(merged_content)} 个内容块")
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {json_file_path}")
    except json.JSONDecodeError:
        print(f"错误：{json_file_path} 不是有效的 JSON 文件")
    except Exception as e:
        print(f"转换过程中发生错误: {str(e)}")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 3:
        print("使用方法: python doc_to_markdown.py <输入JSON文件> <输出Markdown文件>")
        print("示例: python doc_to_markdown.py doc.json output.md")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    json_to_markdown(input_file, output_file)


if __name__ == "__main__":
    main()
