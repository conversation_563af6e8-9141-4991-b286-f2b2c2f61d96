# PDF JSON 转 Markdown 转换器

这是一个用于将从 PDF 解析的 JSON 格式文档转换为 Markdown 格式的 Python 脚本。

## 功能特点

- 支持从 PDF 解析的复杂 JSON 结构中提取文本内容
- 自动识别和格式化标题、列表、段落等不同类型的内容
- 智能合并相同段落内容，不同段落间自动分行
- 支持粗体文本的 Markdown 格式转换
- 自动处理公司信息和研究报告列表
- 长段落自动分割，提高可读性

## 文件说明

- `doc_to_markdown.py` - 基础版本转换脚本
- `doc_to_markdown_final.py` - 最终优化版本（推荐使用）
- `doc.json` - 示例输入文件（PDF 解析结果）
- `output_clean.md` - 手动优化的示例输出
- `output_best.md` - 脚本自动生成的最佳输出

## 使用方法

### 基本用法

```bash
python doc_to_markdown_final.py <输入JSON文件> <输出Markdown文件>
```

### 示例

```bash
python doc_to_markdown_final.py doc.json output.md
```

## 输入格式要求

输入的 JSON 文件应该包含以下结构：

```json
{
  "tree": {
    "root": {
      "children": [
        {
          "type": "section",
          "data": {
            "textlines": [
              {
                "text": "文本内容",
                "bold": false
              }
            ]
          },
          "children": []
        }
      ]
    }
  }
}
```

## 输出格式

脚本会自动识别并转换以下内容类型：

1. **标题** - 使用 `#` 标记
2. **粗体文本** - 使用 `**文本**` 格式
3. **列表项** - 以 `●` 开头的内容转换为 `- 项目`
4. **公司信息** - 以 `【公司名】` 格式的内容转换为三级标题
5. **研究报告** - 自动格式化报告标题和日期
6. **普通段落** - 长段落自动分割，保持可读性

## 特殊处理

- 相同段落内容会合并到一起
- 不同段落之间会自动添加空行分隔
- 长段落（超过300字符）会按句子自动分割
- 公司信息会格式化为独立的章节

## 依赖要求

- Python 3.6+
- 标准库：json, re, typing

## 注意事项

1. 确保输入的 JSON 文件格式正确
2. 输出文件路径必须可写
3. 脚本会自动处理中文字符编码（UTF-8）

## 示例输出

转换后的 Markdown 文件会包含：

- 清晰的标题层次结构
- 格式化的列表和段落
- 正确的中文排版
- 合理的空行分隔

转换完成后，脚本会显示处理的内容块数量和保存路径。
