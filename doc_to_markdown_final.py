#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF JSON 转 Markdown 转换器（最终版本）
将从 PDF 解析的 JSON 格式转换为 Markdown 文件
支持段落合并和格式化
"""

import json
import re
from typing import List, Dict, Any


def extract_text_from_textlines(textlines: List[Dict[str, Any]]) -> List[str]:
    """从 textlines 中提取文本内容，返回文本行列表"""
    if not textlines:
        return []
    
    texts = []
    for line in textlines:
        text = line.get('text', '').strip()
        if text:
            # 检查是否为粗体
            is_bold = line.get('bold', False)
            if is_bold and text:
                text = f"**{text}**"
            texts.append(text)
    
    return texts


def split_long_paragraph(text: str, max_length: int = 200) -> List[str]:
    """将长段落按句子分割"""
    if len(text) <= max_length:
        return [text]
    
    # 按句号、感叹号、问号分割
    sentences = re.split(r'([。！？])', text)
    
    paragraphs = []
    current_para = ""
    
    i = 0
    while i < len(sentences):
        sentence = sentences[i]
        if i + 1 < len(sentences) and sentences[i + 1] in '。！？':
            sentence += sentences[i + 1]
            i += 2
        else:
            i += 1
            
        if not sentence.strip():
            continue
            
        if len(current_para + sentence) > max_length and current_para:
            paragraphs.append(current_para.strip())
            current_para = sentence
        else:
            current_para += sentence
    
    if current_para.strip():
        paragraphs.append(current_para.strip())
    
    return paragraphs


def process_company_info(text: str) -> List[str]:
    """处理包含公司信息的文本"""
    # 按【】分割公司信息
    companies = re.split(r'(【[^】]+】)', text)
    
    result = []
    current_company = ""
    
    for part in companies:
        if part.startswith('【') and part.endswith('】'):
            current_company = part
        elif part.strip() and current_company:
            result.append(f"### {current_company}")
            # 分割长段落
            paragraphs = split_long_paragraph(part.strip())
            result.extend(paragraphs)
            current_company = ""
        elif part.strip():
            result.append(part.strip())
    
    return result


def process_tree_node(node: Dict[str, Any], level: int = 0) -> List[str]:
    """递归处理树节点，提取文本内容"""
    content = []
    
    if not node:
        return content
    
    # 处理当前节点的数据
    if 'data' in node and node['data']:
        data = node['data']
        
        # 处理 textlines
        if 'textlines' in data and data['textlines']:
            text_lines = extract_text_from_textlines(data['textlines'])
            if text_lines:
                # 合并所有文本行
                full_text = ''.join(text_lines)
                
                if full_text.strip():
                    # 根据内容类型决定格式
                    if full_text.startswith('●'):
                        # 列表项
                        content.append(f"- {full_text[1:].strip()}")
                    elif '【' in full_text and '】' in full_text:
                        # 包含公司信息
                        company_content = process_company_info(full_text)
                        content.extend(company_content)
                    elif '》-' in full_text:
                        # 研究报告列表
                        reports = re.split(r'》-(\d{4}\.\d{1,2}\.\d{1,2})', full_text)
                        i = 0
                        while i < len(reports) - 1:
                            if reports[i].strip():
                                title = reports[i].strip()
                                if i + 1 < len(reports):
                                    date = reports[i + 1].strip()
                                    content.append(f"- {title}》 - {date}")
                                    i += 2
                                else:
                                    content.append(f"- {title}》")
                                    i += 1
                            else:
                                i += 1
                    elif len(text_lines) > 1:
                        # 多行文本，每行单独处理
                        for line in text_lines:
                            if line.strip():
                                content.append(line.strip())
                    else:
                        # 单行文本，检查是否需要分段
                        if len(full_text) > 300:
                            paragraphs = split_long_paragraph(full_text)
                            content.extend(paragraphs)
                        else:
                            content.append(full_text)
    
    # 递归处理子节点
    if 'children' in node and node['children']:
        for child in node['children']:
            child_content = process_tree_node(child, level + 1)
            content.extend(child_content)
    
    return content


def clean_and_format_content(content_lines: List[str]) -> List[str]:
    """清理和格式化内容"""
    if not content_lines:
        return []
    
    formatted = []
    
    for i, line in enumerate(content_lines):
        line = line.strip()
        if not line:
            continue
        
        # 添加当前行
        formatted.append(line)
        
        # 决定是否在后面添加空行
        if i < len(content_lines) - 1:
            next_line = content_lines[i + 1].strip()
            if next_line:
                current_is_header = line.startswith('#')
                next_is_header = next_line.startswith('#')
                current_is_list = line.startswith('-')
                next_is_list = next_line.startswith('-')
                current_is_bold = line.startswith('**') and line.endswith('**')
                next_is_bold = next_line.startswith('**') and next_line.endswith('**')
                
                # 在以下情况添加空行：
                # 1. 标题前后
                # 2. 列表和非列表之间
                # 3. 粗体标题后
                if (current_is_header or next_is_header or 
                    (current_is_list != next_is_list) or
                    (current_is_bold and not next_is_bold)):
                    formatted.append('')
    
    return formatted


def json_to_markdown(json_file_path: str, markdown_file_path: str):
    """将 JSON 文件转换为 Markdown 文件"""
    try:
        # 读取 JSON 文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取树结构
        tree = data.get('tree', {})
        root = tree.get('root', {})
        
        # 处理树结构，提取内容
        content_lines = []
        if 'children' in root:
            for child in root['children']:
                child_content = process_tree_node(child, 0)
                content_lines.extend(child_content)
        
        # 清理和格式化内容
        formatted_content = clean_and_format_content(content_lines)
        
        # 写入 Markdown 文件
        with open(markdown_file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(formatted_content))
        
        print(f"转换完成！Markdown 文件已保存到: {markdown_file_path}")
        print(f"共处理了 {len([line for line in formatted_content if line.strip()])} 个内容块")
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {json_file_path}")
    except json.JSONDecodeError:
        print(f"错误：{json_file_path} 不是有效的 JSON 文件")
    except Exception as e:
        print(f"转换过程中发生错误: {str(e)}")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 3:
        print("使用方法: python doc_to_markdown_final.py <输入JSON文件> <输出Markdown文件>")
        print("示例: python doc_to_markdown_final.py doc.json output.md")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    json_to_markdown(input_file, output_file)


if __name__ == "__main__":
    main()
